## **K8S 只读 Go 函数工具集设计文档**

### 1. 概述 (Overview)

本文档旨在设计一个基于 K8S Go SDK (client-go) 的函数工具集（以下简称“本工具集”或“SDK”）。其核心目标是提供一套纯粹的、只读的 Go 函数库，用于安全、高效地查询和观察 Kubernetes 集群状态。

本工具集专为开发、运维（DevOps）和技术支持团队设计，使他们能够在不持有集群写入权限的情况下，通过编程方式进行问题排查、状态监控和自动化巡检。所有功能都将封装成独立的 Go 函数，易于集成到现有的 CLI 工具、Web 服务或自动化脚本中。

### 2. 设计目标 (Goals)

*   **安全只读**: 所有提供的函数必须严格遵守只读原则。功能设计上不包含任何创建、更新、删除（CUD）操作的入口。
*   **功能封装**: 将 `client-go` 的复杂操作封装成简单、易于调用的高级函数，隐藏底层 API 细节。
*   **模块化**: 按功能领域（如资源获取、日志、事件）划分模块，实现高内聚、低耦合。
*   **标准化认证**: 支持标准的 K8S 认证机制，包括 `kubeconfig` 文件和集群内服务账户 (In-Cluster Config)。
*   **易于集成**: 作为 Go 库（SDK）提供，而非命令行工具，方便被其他 Go 应用程序引用和复用。
*   **信息聚合**: 提供超越原生 `kubectl` 的聚合信息能力，例如将资源与其关联的事件聚合在一起，提供一站式信息视图。

### 3. 非目标 (Non-Goals)

*   **不提供写操作**: 本项目不包含任何修改集群状态的功能。
*   **不作为命令行工具发布**: 本项目产物是一个 Go 库，不直接提供可执行的 CLI。
*   **不实现完整的 `kubectl` 功能**: 只专注于最常用、最高频的只读场景，不追求覆盖 `kubectl` 的所有功能。
*   **不包含 UI 界面**: 本项目不涉及任何前端或图形化界面的开发。

### 4. 整体架构 (High-Level Architecture)

本工具集采用分层和模块化的设计思路。

#### 4.1 架构分层

```
+------------------------------------------------+
|           上层应用 (CLI, Web Service等)           |
+------------------------------------------------+
      ↑ (调用本工具集提供的 Go 函数)
+------------------------------------------------+
|               K8S 只读函数工具集 (本项目)         |
| +----------+ +----------+ +----------+ +--------+
| |   Get    | | Describe | |   Logs   | | ...    |
| +----------+ +----------+ +----------+ +--------+
|                  ↓ (依赖)                       |
| +-------------------------------------------+  |
| |             核心客户端模块 (Client)         |  |
| +-------------------------------------------+  |
+------------------------------------------------+
      ↑ (通过 client-go 与 K8S API Server 交互)
+------------------------------------------------+
|              K8S Go SDK (client-go)            |
+------------------------------------------------+
      ↑ (HTTP/S)
+------------------------------------------------+
|            Kubernetes API Server             |
+------------------------------------------------+
```

#### 4.2 模块（包）划分

项目将以 Go Package 的形式组织，每个包负责一个独立的功能领域。

*   `pkg/client`: 负责 K8S 客户端的创建和管理，是所有其他模块的基础。
*   `pkg/get`: 提供通用的资源获取功能，类似 `kubectl get`。
*   `pkg/describe`: 提供资源详情的聚合查询功能，类似 `kubectl describe`。
*   `pkg/logs`: 负责获取 Pod 的容器日志。
*   `pkg/events`: 负责获取和监视集群事件。
*   `pkg/health`: 提供高级的、聚合式的健康状态检查功能。

### 5. 权限模型 (Security & Permissions Model)

本工具集的使用者应被授予一个只读权限的 `ClusterRole`。推荐的 RBAC 配置如下：

*   **Kind**: `ClusterRole`
*   **Name**: `readonly-toolkit-role`
*   **Verbs**: `[get, list, watch]`
*   **Resources**:
    *   `apiGroups: ["", "apps", "batch", "networking.k8s.io", ...]`
    *   `resources: [pods, deployments, services, nodes, events, configmaps, ...]`
    *   `subresources: [pods/log]` (获取日志必需)

使用本工具集的程序应通过一个绑定了此 `ClusterRole` 的 `ServiceAccount` 进行认证。

### 6. 核心模块与函数 API 设计

#### 6.1 `client` 模块

*   **目的**: 提供统一的 K8S 客户端创建入口。
*   **核心数据结构**:
    ```go
    // K8sClient 包含与 K8S API Server 交互所需的各种客户端
    type K8sClient struct {
        Clientset     kubernetes.Interface // 强类型客户端
        DynamicClient dynamic.Interface    // 动态客户端
    }
    ```
*   **核心函数**:
    *   **`NewK8sClient(kubeconfigPath string) (*K8sClient, error)`**
        *   **参数**: `kubeconfigPath` - `kubeconfig` 文件的绝对路径。若为空字符串，则自动尝试 In-Cluster-Config。
        *   **返回**: 初始化好的 `*K8sClient` 实例和可能发生的错误。

#### 6.2 `get` 模块

*   **目的**: 通用地获取一个或多个 K8S 资源。
*   **核心函数**:
    *   **`GetResource(ctx context.Context, client dynamic.Interface, gvr schema.GroupVersionResource, namespace, name string) (*unstructured.Unstructured, error)`**
        *   **参数**: `gvr` - 资源的 Group-Version-Resource；`namespace` - 命名空间（对集群资源为空）；`name` - 资源名。
        *   **返回**: 一个非结构化的资源对象和错误。
    *   **`ListResources(ctx context.Context, client dynamic.Interface, gvr schema.GroupVersionResource, namespace string) (*unstructured.UnstructuredList, error)`**
        *   **参数**: `gvr` - 资源的 Group-Version-Resource；`namespace` - 命名空间（若为空，则查询所有命名空间）。
        *   **返回**: 一个非结构化的资源列表和错误。

#### 6.3 `describe` 模块

*   **目的**: 提供资源的深度信息，聚合其关联数据。
*   **核心数据结构**:
    ```go
    // PodDescription 包含了 Pod 及其相关资源的详细信息
    type PodDescription struct {
        Pod    *corev1.Pod
        Events []corev1.Event
        // ... 可扩展，如关联的 PVC, ConfigMap 等
    }
    ```
*   **核心函数**:
    *   **`DescribePod(ctx context.Context, clientset kubernetes.Interface, namespace, name string) (*PodDescription, error)`**
        *   **参数**: `namespace`, `name` - Pod 的位置和名称。
        *   **返回**: 包含 Pod 自身信息和其关联事件的 `*PodDescription` 结构体。
    *   *（可扩展其他资源的 `Describe` 函数，如 `DescribeDeployment`）*

#### 6.4 `logs` 模块

*   **目的**: 获取容器日志。
*   **核心数据结构**:
    ```go
    // LogOptions 定义了获取日志时的各种选项
    type LogOptions struct {
        Container  string // 容器名
        Follow     bool   // 是否流式输出
        Previous   bool   // 是否获取上一个终止的容器日志
        TailLines  *int64 // 获取最后 N 行
    }
    ```
*   **核心函数**:
    *   **`GetLogsStream(ctx context.Context, clientset kubernetes.Interface, namespace, podName string, opts LogOptions) (io.ReadCloser, error)`**
        *   **参数**: `podName`, `opts` - 指定 Pod 和日志选项。
        *   **返回**: 一个 `io.ReadCloser` 接口，调用者可从此流中读取日志数据。**调用者有责任关闭此流。**

#### 6.5 `events` 模块

*   **目的**: 获取和监视集群事件。
*   **核心函数**:
    *   **`ListEvents(ctx context.Context, clientset kubernetes.Interface, namespace string) (*corev1.EventList, error)`**
        *   **参数**: `namespace` - 命名空间（若为空，则查询所有命名空间）。
        *   **返回**: 事件列表。
    *   **`WatchEvents(ctx context.Context, clientset kubernetes.Interface, namespace string) (watch.Interface, error)`**
        *   **参数**: `namespace` - 命名空间（若为空，则查询所有命名空间）。
        *   **返回**: 一个 `watch.Interface` 接口，调用者可从中读取事件流。

#### 6.6 `health` 模块

*   **目的**: 提供高级的、聚合的健康检查报告。
*   **核心数据结构**:
    ```go
    // HealthStatus 代表一个检查项的状态和信息
    type HealthStatus struct {
        IsHealthy bool
        Message   string
        Details   interface{} // 存储详细的故障信息或数据
    }

    // ClusterHealthReport 包含了整个集群的健康检查结果
    type ClusterHealthReport struct {
        NodeStatus           HealthStatus
        SystemComponentStatus HealthStatus
        // ... 可扩展，如 ApplicationWorkloadStatus
    }
    ```
*   **核心函数**:
    *   **`CheckClusterHealth(ctx context.Context, clientset kubernetes.Interface) (*ClusterHealthReport, error)`**
        *   **参数**: 无。
        *   **返回**: 一个结构化的健康报告，包含了对节点、核心组件等多维度的检查结果。

### 7. 错误处理 (Error Handling)

*   所有函数都将遵循 Go 的标准错误处理模式，返回 `(result, error)`。
*   错误将尽可能地包装 `client-go` 返回的原始错误，以保留上下文。
*   对于常见的错误（如资源未找到、权限不足），将定义特定的错误类型或使用 `errors.Is` 进行判断，方便调用者进行程序化处理。

### 8. 测试策略 (Testing Strategy)

*   **单元测试**: 对每个模块的核心函数进行单元测试。使用 `k8s.io/client-go/testing` 包来模拟 K8S API Server 的行为，验证函数的逻辑正确性。
*   **集成测试**: 编写可选的集成测试，这些测试需要一个真实的 K8S 集群（如 `kind` 或 `minikube`）来运行，验证与真实 API Server 的交互是否符合预期。