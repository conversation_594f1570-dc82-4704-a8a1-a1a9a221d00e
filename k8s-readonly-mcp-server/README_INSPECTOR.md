# Kubernetes 只读工具集 - Inspector 模块

基于 design.md 设计的全面 Kubernetes 只读检查工具集，实现了安全、高效的集群状态观察和诊断功能。

## 🚀 特性

- **全面只读**：严格遵循只读原则，无任何写操作
- **并发收集**：使用 goroutines 并发收集各类资源，提高性能
- **完整覆盖**：涵盖所有常见 Kubernetes 资源类型
- **灵活查询**：支持集群级、命名空间级、条件筛选等多种查询方式
- **结构化输出**：统一 JSON 格式输出，便于集成和分析
- **错误容忍**：遇到权限或网络问题时继续收集可用数据

## 📦 模块结构

```
pkg/
├── client/          # 客户端创建和管理
├── inspector/       # 🔥 新增：全面检查工具
├── describe/        # 资源详细描述
├── health/          # 集群健康检查
└── ...
```

## 🛠️ 核心功能

### 1. Inspector 模块 (`pkg/inspector`)

#### 主要功能

- **GetFullClusterSnapshot**: 获取整个集群的完整快照
- **GetNamespaceSnapshot**: 获取指定命名空间的完整快照
- **GetQuickSummary**: 获取集群快速摘要
- **SearchResources**: 搜索包含特定关键词的资源
- **ExportToJSON**: 将快照导出为 JSON 格式

#### 收集的资源类型

| 类别 | 资源类型 |
|------|----------|
| **Core** | Nodes, Namespaces, Pods, Services, ConfigMaps, Secrets, PVCs, PVs |
| **Workloads** | Deployments, StatefulSets, DaemonSets, ReplicaSets, Jobs, CronJobs |
| **Networking** | Ingresses, NetworkPolicies |
| **Storage** | StorageClasses |
| **RBAC** | ServiceAccounts, Roles, ClusterRoles, RoleBindings, ClusterRoleBindings |
| **Events** | All cluster events |

### 2. MCP 工具接口

通过 MCP 协议提供以下工具：

#### `cluster_inspect`
获取整个集群的完整只读快照
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "resource_counts": {
    "nodes": 3,
    "pods": 45,
    "deployments": 12,
    ...
  },
  "nodes": [...],
  "pods": [...],
  ...
}
```

#### `namespace_inspect`
获取指定命名空间的完整快照
```json
{
  "namespace": "default",
  "timestamp": "2024-01-01T12:00:00Z",
  "resource_counts": {...},
  "pods": [...],
  "services": [...],
  ...
}
```

#### `cluster_health`
检查集群健康状况
```json
{
  "NodeStatus": {"IsHealthy": true, "Message": "All 3 nodes are ready"},
  "SystemComponentStatus": {"IsHealthy": true, "Message": "All 15 system pods are running"}
}
```

#### `cluster_summary`
获取集群快速摘要
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "total_nodes": 3,
  "total_namespaces": 5,
  "total_pods": 45,
  "pod_phases": {"Running": 40, "Pending": 3, "Failed": 2}
}
```

#### `search_resources`
搜索包含特定关键词的资源
```json
{
  "pods.v1/": [...],
  "deployments.apps/v1": [...]
}
```

## 🔧 使用方法

### 1. 作为库使用

```go
package main

import (
    "context"
    "log"
    
    "k8s-readonly-mcp-server/pkg/client"
    "k8s-readonly-mcp-server/pkg/inspector"
)

func main() {
    // 创建 Kubernetes 客户端
    k8sClient, err := client.NewK8sClient("")
    if err != nil {
        log.Fatal(err)
    }

    // 创建 Inspector 实例
    ins := inspector.NewInspector(k8sClient.Clientset, k8sClient.DynamicClient)

    ctx := context.Background()

    // 获取完整集群快照
    snapshot, err := ins.GetFullClusterSnapshot(ctx)
    if err != nil {
        log.Fatal(err)
    }

    // 获取命名空间快照
    nsSnapshot, err := ins.GetNamespaceSnapshot(ctx, "default")
    if err != nil {
        log.Fatal(err)
    }

    // 获取快速摘要
    summary, err := ins.GetQuickSummary(ctx)
    if err != nil {
        log.Fatal(err)
    }

    log.Printf("Total resources: %d", snapshot.ResourceCounts["total"])
    log.Printf("Default namespace resources: %d", nsSnapshot.ResourceCounts["total"])
    log.Printf("Cluster nodes: %v", summary["total_nodes"])
}
```

### 2. 作为 MCP 服务器使用

```bash
# 启动 MCP 服务器
go run main_new.go

# 服务器将在 :8080 端口监听
# 可用工具：
# - cluster_inspect
# - namespace_inspect
# - cluster_health
# - kubectl_describe
# - cluster_summary
# - search_resources
```

### 3. 示例程序

运行示例程序体验功能：

```bash
# 运行示例
go run examples/inspector_example.go
```

## 🔐 权限要求

只需要只读权限的 ClusterRole：

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: readonly-inspector-role
rules:
- apiGroups: ["", "apps", "batch", "networking.k8s.io", "storage.k8s.io", "rbac.authorization.k8s.io"]
  resources: ["*"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods/log"]
  verbs: ["get", "list"]
```

## ⚡ 性能优化

- **并发收集**：使用 goroutines 并发收集不同类型的资源
- **超时控制**：建议配合 context timeout 使用
- **内存管理**：大集群建议使用流式处理或分批获取

## 🎯 典型使用场景

1. **集群审计**：定期获取集群完整状态快照
2. **故障排查**：快速定位异常资源和事件
3. **容量规划**：分析资源使用情况和分布
4. **安全检查**：扫描敏感资源配置
5. **迁移准备**：收集源集群完整信息
6. **监控告警**：集成到监控系统进行状态检查

## 📊 输出示例

### 集群快照摘要
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "resource_counts": {
    "nodes": 3,
    "namespaces": 5,
    "pods": 45,
    "deployments": 12,
    "services": 18,
    "configmaps": 25,
    "secrets": 15,
    "total": 123
  },
  "error_summary": []
}
```

### 快速摘要
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "total_nodes": 3,
  "total_namespaces": 5,
  "total_pods": 45,
  "pod_phases": {
    "Running": 40,
    "Pending": 3,
    "Failed": 2,
    "Succeeded": 0
  }
}
```