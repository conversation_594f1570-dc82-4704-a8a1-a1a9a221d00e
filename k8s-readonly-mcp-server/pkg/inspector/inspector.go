package inspector

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	storagev1 "k8s.io/api/storage/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
)

// ClusterSnapshot 包含集群的完整快照
type ClusterSnapshot struct {
	Timestamp time.Time `json:"timestamp"`
	Namespace string    `json:"namespace,omitempty"`

	// Core Resources
	Nodes       []corev1.Node       `json:"nodes,omitempty"`
	Namespaces  []corev1.Namespace  `json:"namespaces,omitempty"`
	Pods        []corev1.Pod        `json:"pods,omitempty"`
	Services    []corev1.Service    `json:"services,omitempty"`
	ConfigMaps  []corev1.ConfigMap  `json:"configmaps,omitempty"`
	Secrets     []corev1.Secret     `json:"secrets,omitempty"`
	PVCs        []corev1.PersistentVolumeClaim `json:"pvcs,omitempty"`
	PVs         []corev1.PersistentVolume      `json:"pvs,omitempty"`

	// Workloads
	Deployments  []appsv1.Deployment  `json:"deployments,omitempty"`
	StatefulSets []appsv1.StatefulSet `json:"statefulsets,omitempty"`
	DaemonSets   []appsv1.DaemonSet   `json:"daemonsets,omitempty"`
	ReplicaSets  []appsv1.ReplicaSet  `json:"replicasets,omitempty"`
	Jobs         []batchv1.Job        `json:"jobs,omitempty"`
	CronJobs     []batchv1.CronJob    `json:"cronjobs,omitempty"`

	// Networking
	Ingresses    []networkingv1.Ingress `json:"ingresses,omitempty"`
	NetworkPolicies []networkingv1.NetworkPolicy `json:"networkpolicies,omitempty"`

	// Storage
	StorageClasses []storagev1.StorageClass `json:"storageclasses,omitempty"`

	// RBAC
	ServiceAccounts []corev1.ServiceAccount `json:"serviceaccounts,omitempty"`
	Roles           []rbacv1.Role          `json:"roles,omitempty"`
	ClusterRoles    []rbacv1.ClusterRole   `json:"clusterroles,omitempty"`
	RoleBindings    []rbacv1.RoleBinding   `json:"rolebindings,omitempty"`
	ClusterRoleBindings []rbacv1.ClusterRoleBinding `json:"clusterrolebindings,omitempty"`

	// Events
	Events []corev1.Event `json:"events,omitempty"`

	// Summary Statistics
	ResourceCounts map[string]int `json:"resource_counts,omitempty"`
	ErrorSummary   []string       `json:"error_summary,omitempty"`
}

// Inspector 提供全面的集群只读检查功能
type Inspector struct {
	clientset     kubernetes.Interface
	dynamicClient dynamic.Interface
}

// NewInspector 创建一个新的 Inspector 实例
func NewInspector(clientset kubernetes.Interface, dynamicClient dynamic.Interface) *Inspector {
	return &Inspector{
		clientset:     clientset,
		dynamicClient: dynamicClient,
	}
}

// GetFullClusterSnapshot 获取整个集群的完整快照
func (i *Inspector) GetFullClusterSnapshot(ctx context.Context) (*ClusterSnapshot, error) {
	return i.getClusterSnapshot(ctx, "")
}

// GetNamespaceSnapshot 获取指定命名空间的完整快照
func (i *Inspector) GetNamespaceSnapshot(ctx context.Context, namespace string) (*ClusterSnapshot, error) {
	if namespace == "" {
		return nil, fmt.Errorf("namespace cannot be empty")
	}
	return i.getClusterSnapshot(ctx, namespace)
}

// getClusterSnapshot 是内部实现，支持获取整个集群或单个命名空间的快照
func (i *Inspector) getClusterSnapshot(ctx context.Context, targetNamespace string) (*ClusterSnapshot, error) {
	snapshot := &ClusterSnapshot{
		Timestamp:      time.Now(),
		Namespace:      targetNamespace,
		ResourceCounts: make(map[string]int),
		ErrorSummary:   []string{},
	}

	var wg sync.WaitGroup
	var mu sync.Mutex

	// Helper function to record errors
	recordError := func(format string, args ...interface{}) {
		mu.Lock()
		defer mu.Unlock()
		errMsg := fmt.Sprintf(format, args...)
		snapshot.ErrorSummary = append(snapshot.ErrorSummary, errMsg)
	}

	// Helper function to increment count
	incrementCount := func(resourceType string, count int) {
		mu.Lock()
		defer mu.Unlock()
		snapshot.ResourceCounts[resourceType] += count
	}

	// Define all resource collection tasks
	tasks := []struct {
		name     string
		collect  func() error
	}{
		{"Nodes", func() error {
			nodes, err := i.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
			if err != nil {
				recordError("Failed to list nodes: %v", err)
				return err
			}
			snapshot.Nodes = nodes.Items
			incrementCount("nodes", len(nodes.Items))
			return nil
		}},
		{"Namespaces", func() error {
			if targetNamespace != "" {
				ns, err := i.clientset.CoreV1().Namespaces().Get(ctx, targetNamespace, metav1.GetOptions{})
				if err != nil {
					recordError("Failed to get namespace %s: %v", targetNamespace, err)
					return err
				}
				snapshot.Namespaces = []corev1.Namespace{*ns}
				incrementCount("namespaces", 1)
			} else {
				namespaces, err := i.clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
				if err != nil {
					recordError("Failed to list namespaces: %v", err)
					return err
				}
				snapshot.Namespaces = namespaces.Items
				incrementCount("namespaces", len(namespaces.Items))
			}
			return nil
		}},
		{"Pods", func() error {
			var pods *corev1.PodList
			var err error
			if targetNamespace != "" {
				pods, err = i.clientset.CoreV1().Pods(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				pods, err = i.clientset.CoreV1().Pods("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list pods: %v", err)
				return err
			}
			snapshot.Pods = pods.Items
			incrementCount("pods", len(pods.Items))
			return nil
		}},
		{"Services", func() error {
			var services *corev1.ServiceList
			var err error
			if targetNamespace != "" {
				services, err = i.clientset.CoreV1().Services(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				services, err = i.clientset.CoreV1().Services("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list services: %v", err)
				return err
			}
			snapshot.Services = services.Items
			incrementCount("services", len(services.Items))
			return nil
		}},
		{"ConfigMaps", func() error {
			var cms *corev1.ConfigMapList
			var err error
			if targetNamespace != "" {
				cms, err = i.clientset.CoreV1().ConfigMaps(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				cms, err = i.clientset.CoreV1().ConfigMaps("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list configmaps: %v", err)
				return err
			}
			snapshot.ConfigMaps = cms.Items
			incrementCount("configmaps", len(cms.Items))
			return nil
		}},
		{"Secrets", func() error {
			var secrets *corev1.SecretList
			var err error
			if targetNamespace != "" {
				secrets, err = i.clientset.CoreV1().Secrets(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				secrets, err = i.clientset.CoreV1().Secrets("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list secrets: %v", err)
				return err
			}
			snapshot.Secrets = secrets.Items
			incrementCount("secrets", len(secrets.Items))
			return nil
		}},
		{"PVCs", func() error {
			var pvcs *corev1.PersistentVolumeClaimList
			var err error
			if targetNamespace != "" {
				pvcs, err = i.clientset.CoreV1().PersistentVolumeClaims(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				pvcs, err = i.clientset.CoreV1().PersistentVolumeClaims("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list pvcs: %v", err)
				return err
			}
			snapshot.PVCs = pvcs.Items
			incrementCount("pvcs", len(pvcs.Items))
			return nil
		}},
		{"PVs", func() error {
			pvs, err := i.clientset.CoreV1().PersistentVolumes().List(ctx, metav1.ListOptions{})
			if err != nil {
				recordError("Failed to list pvs: %v", err)
				return err
			}
			snapshot.PVs = pvs.Items
			incrementCount("pvs", len(pvs.Items))
			return nil
		}},
		{"Deployments", func() error {
			var deps *appsv1.DeploymentList
			var err error
			if targetNamespace != "" {
				deps, err = i.clientset.AppsV1().Deployments(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				deps, err = i.clientset.AppsV1().Deployments("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list deployments: %v", err)
				return err
			}
			snapshot.Deployments = deps.Items
			incrementCount("deployments", len(deps.Items))
			return nil
		}},
		{"StatefulSets", func() error {
			var sts *appsv1.StatefulSetList
			var err error
			if targetNamespace != "" {
				sts, err = i.clientset.AppsV1().StatefulSets(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				sts, err = i.clientset.AppsV1().StatefulSets("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list statefulsets: %v", err)
				return err
			}
			snapshot.StatefulSets = sts.Items
			incrementCount("statefulsets", len(sts.Items))
			return nil
		}},
		{"DaemonSets", func() error {
			var dss *appsv1.DaemonSetList
			var err error
			if targetNamespace != "" {
				dss, err = i.clientset.AppsV1().DaemonSets(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				dss, err = i.clientset.AppsV1().DaemonSets("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list daemonsets: %v", err)
				return err
			}
			snapshot.DaemonSets = dss.Items
			incrementCount("daemonsets", len(dss.Items))
			return nil
		}},
		{"ReplicaSets", func() error {
			var rss *appsv1.ReplicaSetList
			var err error
			if targetNamespace != "" {
				rss, err = i.clientset.AppsV1().ReplicaSets(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				rss, err = i.clientset.AppsV1().ReplicaSets("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list replicasets: %v", err)
				return err
			}
			snapshot.ReplicaSets = rss.Items
			incrementCount("replicasets", len(rss.Items))
			return nil
		}},
		{"Jobs", func() error {
			var jobs *batchv1.JobList
			var err error
			if targetNamespace != "" {
				jobs, err = i.clientset.BatchV1().Jobs(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				jobs, err = i.clientset.BatchV1().Jobs("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list jobs: %v", err)
				return err
			}
			snapshot.Jobs = jobs.Items
			incrementCount("jobs", len(jobs.Items))
			return nil
		}},
		{"CronJobs", func() error {
			var cronjobs *batchv1.CronJobList
			var err error
			if targetNamespace != "" {
				cronjobs, err = i.clientset.BatchV1().CronJobs(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				cronjobs, err = i.clientset.BatchV1().CronJobs("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list cronjobs: %v", err)
				return err
			}
			snapshot.CronJobs = cronjobs.Items
			incrementCount("cronjobs", len(cronjobs.Items))
			return nil
		}},
		{"Ingresses", func() error {
			var ingresses *networkingv1.IngressList
			var err error
			if targetNamespace != "" {
				ingresses, err = i.clientset.NetworkingV1().Ingresses(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				ingresses, err = i.clientset.NetworkingV1().Ingresses("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list ingresses: %v", err)
				return err
			}
			snapshot.Ingresses = ingresses.Items
			incrementCount("ingresses", len(ingresses.Items))
			return nil
		}},
		{"NetworkPolicies", func() error {
			var nps *networkingv1.NetworkPolicyList
			var err error
			if targetNamespace != "" {
				nps, err = i.clientset.NetworkingV1().NetworkPolicies(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				nps, err = i.clientset.NetworkingV1().NetworkPolicies("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list networkpolicies: %v", err)
				return err
			}
			snapshot.NetworkPolicies = nps.Items
			incrementCount("networkpolicies", len(nps.Items))
			return nil
		}},
		{"StorageClasses", func() error {
			scs, err := i.clientset.StorageV1().StorageClasses().List(ctx, metav1.ListOptions{})
			if err != nil {
				recordError("Failed to list storageclasses: %v", err)
				return err
			}
			snapshot.StorageClasses = scs.Items
			incrementCount("storageclasses", len(scs.Items))
			return nil
		}},
		{"ServiceAccounts", func() error {
			var sas *corev1.ServiceAccountList
			var err error
			if targetNamespace != "" {
				sas, err = i.clientset.CoreV1().ServiceAccounts(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				sas, err = i.clientset.CoreV1().ServiceAccounts("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list serviceaccounts: %v", err)
				return err
			}
			snapshot.ServiceAccounts = sas.Items
			incrementCount("serviceaccounts", len(sas.Items))
			return nil
		}},
		{"Roles", func() error {
			var roles *rbacv1.RoleList
			var err error
			if targetNamespace != "" {
				roles, err = i.clientset.RbacV1().Roles(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				roles, err = i.clientset.RbacV1().Roles("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list roles: %v", err)
				return err
			}
			snapshot.Roles = roles.Items
			incrementCount("roles", len(roles.Items))
			return nil
		}},
		{"ClusterRoles", func() error {
			clusterRoles, err := i.clientset.RbacV1().ClusterRoles().List(ctx, metav1.ListOptions{})
			if err != nil {
				recordError("Failed to list clusterroles: %v", err)
				return err
			}
			snapshot.ClusterRoles = clusterRoles.Items
			incrementCount("clusterroles", len(clusterRoles.Items))
			return nil
		}},
		{"RoleBindings", func() error {
			var rbs *rbacv1.RoleBindingList
			var err error
			if targetNamespace != "" {
				rbs, err = i.clientset.RbacV1().RoleBindings(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				rbs, err = i.clientset.RbacV1().RoleBindings("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list rolebindings: %v", err)
				return err
			}
			snapshot.RoleBindings = rbs.Items
			incrementCount("rolebindings", len(rbs.Items))
			return nil
		}},
		{"ClusterRoleBindings", func() error {
			crbs, err := i.clientset.RbacV1().ClusterRoleBindings().List(ctx, metav1.ListOptions{})
			if err != nil {
				recordError("Failed to list clusterrolebindings: %v", err)
				return err
			}
			snapshot.ClusterRoleBindings = crbs.Items
			incrementCount("clusterrolebindings", len(crbs.Items))
			return nil
		}},
		{"Events", func() error {
			var events *corev1.EventList
			var err error
			if targetNamespace != "" {
				events, err = i.clientset.CoreV1().Events(targetNamespace).List(ctx, metav1.ListOptions{})
			} else {
				events, err = i.clientset.CoreV1().Events("").List(ctx, metav1.ListOptions{})
			}
			if err != nil {
				recordError("Failed to list events: %v", err)
				return err
			}
			snapshot.Events = events.Items
			incrementCount("events", len(events.Items))
			return nil
		}},
	}

	// Execute all tasks concurrently
	for _, task := range tasks {
		wg.Add(1)
		go func(t struct{name string; collect func() error}) {
			defer wg.Done()
			if err := t.collect(); err != nil {
				recordError("Task %s failed: %v", t.name, err)
			}
		}(task)
	}

	wg.Wait()

	// Calculate total resources
	totalResources := 0
	for _, count := range snapshot.ResourceCounts {
		totalResources += count
	}
	snapshot.ResourceCounts["total"] = totalResources

	return snapshot, nil
}

// GetCustomResources 获取自定义资源(CRDs)的快照
func (i *Inspector) GetCustomResources(ctx context.Context, group, version, resource string) ([]unstructured.Unstructured, error) {
	gvr := schema.GroupVersionResource{
		Group:    group,
		Version:  version,
		Resource: resource,
	}

	list, err := i.dynamicClient.Resource(gvr).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list custom resources %s.%s/%s: %v", resource, group, version, err)
	}

	return list.Items, nil
}

// FilterByLabels 根据标签筛选资源
func (i *Inspector) FilterByLabels(resources []unstructured.Unstructured, labels map[string]string) []unstructured.Unstructured {
	var filtered []unstructured.Unstructured
	
	for _, res := range resources {
		resLabels := res.GetLabels()
		match := true
		
		for key, value := range labels {
			if resLabels[key] != value {
				match = false
				break
			}
		}
		
		if match {
			filtered = append(filtered, res)
		}
	}
	
	return filtered
}

// ExportToJSON 将快照导出为JSON格式
func (i *Inspector) ExportToJSON(snapshot *ClusterSnapshot) (string, error) {
	data, err := json.MarshalIndent(snapshot, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal snapshot to JSON: %v", err)
	}
	return string(data), nil
}

// GetQuickSummary 获取集群的快速摘要
func (i *Inspector) GetQuickSummary(ctx context.Context) (map[string]interface{}, error) {
	summary := make(map[string]interface{})
	
	// Get basic info
	nodes, err := i.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list nodes: %v", err)
	}
	
	namespaces, err := i.clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list namespaces: %v", err)
	}
	
	allPods, err := i.clientset.CoreV1().Pods("").List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods: %v", err)
	}
	
	// Count pods by phase
	podPhases := make(map[string]int)
	for _, pod := range allPods.Items {
		podPhases[string(pod.Status.Phase)]++
	}
	
	// Basic summary
	summary["timestamp"] = time.Now()
	summary["total_nodes"] = len(nodes.Items)
	summary["total_namespaces"] = len(namespaces.Items)
	summary["total_pods"] = len(allPods.Items)
	summary["pod_phases"] = podPhases
	
	return summary, nil
}

// SearchResources 搜索包含特定字符串的资源
func (i *Inspector) SearchResources(ctx context.Context, searchTerm string) (map[string][]unstructured.Unstructured, error) {
	results := make(map[string][]unstructured.Unstructured)
	
	// Common GVRs to search
	commonGVRs := []schema.GroupVersionResource{
		{Group: "", Version: "v1", Resource: "pods"},
		{Group: "", Version: "v1", Resource: "services"},
		{Group: "apps", Version: "v1", Resource: "deployments"},
		{Group: "apps", Version: "v1", Resource: "statefulsets"},
		{Group: "", Version: "v1", Resource: "configmaps"},
		{Group: "", Version: "v1", Resource: "secrets"},
	}
	
	for _, gvr := range commonGVRs {
		list, err := i.dynamicClient.Resource(gvr).List(ctx, metav1.ListOptions{})
		if err != nil {
			continue // Skip resources we can't access
		}
		
		var matched []unstructured.Unstructured
		for _, item := range list.Items {
			// Search in name, labels, annotations
			if strings.Contains(item.GetName(), searchTerm) ||
			   strings.Contains(item.GetNamespace(), searchTerm) {
				matched = append(matched, item)
				continue
			}
			
			labels := item.GetLabels()
			for k, v := range labels {
				if strings.Contains(k, searchTerm) || strings.Contains(v, searchTerm) {
					matched = append(matched, item)
					break
				}
			}
		}
		
		if len(matched) > 0 {
			key := fmt.Sprintf("%s.%s/%s", gvr.Resource, gvr.Group, gvr.Version)
			results[key] = matched
		}
	}
	
	return results, nil
}