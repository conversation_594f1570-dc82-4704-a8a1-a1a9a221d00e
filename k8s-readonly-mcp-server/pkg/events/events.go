package events

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/client-go/kubernetes"
)

// ListEvents lists events in the specified namespace
// If namespace is empty, it lists events across all namespaces
func ListEvents(ctx context.Context, clientset kubernetes.Interface, namespace string) (*corev1.EventList, error) {
	if namespace == "" {
		return clientset.CoreV1().Events("").List(ctx, metav1.ListOptions{})
	}
	return clientset.CoreV1().Events(namespace).List(ctx, metav1.ListOptions{})
}

// WatchEvents watches events in the specified namespace
// If namespace is empty, it watches events across all namespaces
func WatchEvents(ctx context.Context, clientset kubernetes.Interface, namespace string) (watch.Interface, error) {
	if namespace == "" {
		return clientset.CoreV1().Events("").Watch(ctx, metav1.ListOptions{})
	}
	return clientset.CoreV1().Events(namespace).Watch(ctx, metav1.ListOptions{})
}