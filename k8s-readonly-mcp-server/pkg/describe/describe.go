package describe

import (
	"context"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// PodDescription 包含了 Pod 及其相关资源的详细信息
type PodDescription struct {
	Pod    *corev1.Pod
	Events []corev1.Event
	// ... 可扩展，如关联的 PVC, ConfigMap 等
}

// DescribePod provides detailed information about a pod, aggregating its associated data
func DescribePod(ctx context.Context, clientset kubernetes.Interface, namespace, name string) (*PodDescription, error) {
	pod, err := clientset.CoreV1().Pods(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	// Get events related to the pod
	events, err := clientset.CoreV1().Events(namespace).List(ctx, metav1.ListOptions{
		FieldSelector: "involvedObject.name=" + name,
	})
	if err != nil {
		return nil, err
	}

	return &PodDescription{
		Pod:    pod,
		Events: events.Items,
	}, nil
}