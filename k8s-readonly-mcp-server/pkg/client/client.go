package client

import (
	"errors"
	"fmt"
	"log"
	"sync"

	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	// 确保导入 clientcmd/api 以便加载配置
	_ "k8s.io/client-go/plugin/pkg/client/auth"
)

// K8sClient k8s客户端
type K8sClient struct {
	ClientSet     kubernetes.Interface
	DynamicClient dynamic.Interface
}

var (
	instance *K8sClient
	once     sync.Once
)

// getKubeConfig 智能获取 rest.Config，优先使用 In-Cluster 配置。
// 这是实现两种模式连接的核心。
func getKubeConfig() (*rest.Config, error) {
	// 1. 尝试 In-Cluster 配置
	// 当你的程序作为 Pod 部署在 K8s 集群中时，K8s 会自动注入
	// ServiceAccount token 和 API Server 的 CA 证书到 Pod 中。
	// InClusterConfig() 会读取这些信息来创建配置。
	config, err := rest.InClusterConfig()
	if err == nil {
		log.Println("Successfully configured client using in-cluster config")
		return config, nil
	}

	// 如果 InClusterConfig() 返回错误，最常见的是 rest.ErrNotInCluster。
	// 这说明程序没有运行在 Pod 中，我们需要回退到 Kubeconfig 文件的方式。
	if !errors.Is(err, rest.ErrNotInCluster) {
		return nil, fmt.Errorf("unexpected error while creating in-cluster config: %w", err)
	}
	log.Println("Not running in-cluster, falling back to kubeconfig")

	// 2. 回退到 Out-of-Cluster 配置 (使用 kubeconfig 文件)
	// 使用我们之前讨论过的健壮的加载方式。
	loadingRules := clientcmd.NewDefaultClientConfigLoadingRules()
	configOverrides := &clientcmd.ConfigOverrides{}
	kubeConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(loadingRules, configOverrides)

	config, err = kubeConfig.ClientConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to load out-of-cluster config: %w", err)
	}
	log.Println("Successfully configured client using out-of-cluster config")
	return config, nil
}

// NewK8sClient 创建一个新的 K8sClient 实例。
// 它现在能自动适应集群内外环境。
func NewK8sClient() (*K8sClient, error) {
	// 获取配置（自动判断是 in-cluster 还是 out-of-cluster）
	config, err := getKubeConfig()
	if err != nil {
		return nil, fmt.Errorf("could not get kubernetes config: %w", err)
	}

	// 根据配置创建各种客户端
	cs, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("create clientset: %w", err)
	}
	dc, err := dynamic.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("create dynamic client: %w", err)
	}
	return &K8sClient{ClientSet: cs, DynamicClient: dc}, nil
}

// GetK8sClient 单例模式，获取 K8sClient
func GetK8sClient() *K8sClient {
	once.Do(func() {
		client, err := NewK8sClient()
		if err != nil {
			log.Fatalf("Failed to create Kubernetes client: %v", err)
		}
		instance = client
	})
	return instance
}
