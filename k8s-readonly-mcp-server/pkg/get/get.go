package get

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
)

/*
KubectlGet 等价于 kubectl get <resource> [-n <ns>] [<name>]
resourceType: pods / svc / deploy / cm / secret / node …
namespace:    空串表示「所有命名空间」（集群级资源同样适用）
name:         空串表示「列全部」
*/
func KubectlGet(
	ctx context.Context,
	cli dynamic.Interface,
	resourceType, namespace, name string,
) (*unstructured.UnstructuredList, error) {

	// 1. 把常见别名硬编码成 GVR
	gvr, ok := alias2GVR[resourceType]
	if !ok {
		return nil, fmt.Errorf("unknown resource %q", resourceType)
	}

	// 2. 构造 ResourceInterface
	var resIf dynamic.ResourceInterface
	if namespace == "" {
		resIf = cli.Resource(gvr)
	} else {
		resIf = cli.Resource(gvr).Namespace(namespace)
	}

	// 3. 取单个 or 列表
	if name != "" {
		obj, err := resIf.Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		return &unstructured.UnstructuredList{Items: []unstructured.Unstructured{*obj}}, nil
	}

	return resIf.List(ctx, metav1.ListOptions{})
}

/* ---------- 私有：别名 -> GVR 表 ---------- */
var alias2GVR = map[string]schema.GroupVersionResource{
	"pods":        {Group: "", Version: "v1", Resource: "pods"},
	"po":          {Group: "", Version: "v1", Resource: "pods"},
	"services":    {Group: "", Version: "v1", Resource: "services"},
	"svc":         {Group: "", Version: "v1", Resource: "services"},
	"configmaps":  {Group: "", Version: "v1", Resource: "configmaps"},
	"cm":          {Group: "", Version: "v1", Resource: "configmaps"},
	"secrets":     {Group: "", Version: "v1", Resource: "secrets"},
	"deployments": {Group: "apps", Version: "v1", Resource: "deployments"},
	"deploy":      {Group: "apps", Version: "v1", Resource: "deployments"},
	"nodes":       {Group: "", Version: "v1", Resource: "nodes"},
	"node":        {Group: "", Version: "v1", Resource: "nodes"},
	"namespaces":  {Group: "", Version: "v1", Resource: "namespaces"},
	"ns":          {Group: "", Version: "v1", Resource: "namespaces"},
}
