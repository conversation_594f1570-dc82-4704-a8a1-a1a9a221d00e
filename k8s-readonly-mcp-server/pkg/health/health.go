package health

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// HealthStatus represents the status and information of a health check item
type HealthStatus struct {
	IsHealthy bool
	Message   string
	Details   interface{} // Store detailed failure information or data
}

// ClusterHealthReport contains the health check results for the entire cluster
type ClusterHealthReport struct {
	NodeStatus           HealthStatus
	SystemComponentStatus HealthStatus
	// ... 可扩展，如 ApplicationWorkloadStatus
}

// CheckClusterHealth provides an advanced, aggregated health report
func CheckClusterHealth(ctx context.Context, clientset kubernetes.Interface) (*ClusterHealthReport, error) {
	report := &ClusterHealthReport{}

	// Check node status
	nodeStatus, err := checkNodeHealth(ctx, clientset)
	if err != nil {
		return nil, fmt.Errorf("failed to check node health: %v", err)
	}
	report.NodeStatus = *nodeStatus

	// Check system component status
	componentStatus, err := checkComponentHealth(ctx, clientset)
	if err != nil {
		return nil, fmt.Errorf("failed to check component health: %v", err)
	}
	report.SystemComponentStatus = *componentStatus

	return report, nil
}

func checkNodeHealth(ctx context.Context, clientset kubernetes.Interface) (*HealthStatus, error) {
	nodes, err := clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return &HealthStatus{
			IsHealthy: false,
			Message:   "Failed to list nodes",
			Details:   err.Error(),
		}, nil
	}

	unhealthyNodes := 0
	for _, node := range nodes.Items {
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeReady && condition.Status != corev1.ConditionTrue {
				unhealthyNodes++
				break
			}
		}
	}

	if unhealthyNodes > 0 {
		return &HealthStatus{
			IsHealthy: false,
			Message:   fmt.Sprintf("%d/%d nodes are not ready", unhealthyNodes, len(nodes.Items)),
		}, nil
	}

	return &HealthStatus{
		IsHealthy: true,
		Message:   fmt.Sprintf("All %d nodes are ready", len(nodes.Items)),
	}, nil
}

func checkComponentHealth(ctx context.Context, clientset kubernetes.Interface) (*HealthStatus, error) {
	// Check if kube-system pods are running as a proxy for system component health
	pods, err := clientset.CoreV1().Pods("kube-system").List(ctx, metav1.ListOptions{})
	if err != nil {
		return &HealthStatus{
			IsHealthy: false,
			Message:   "Failed to list system pods",
			Details:   err.Error(),
		}, nil
	}

	unhealthyPods := 0
	for _, pod := range pods.Items {
		if pod.Status.Phase != corev1.PodRunning && pod.Status.Phase != corev1.PodSucceeded {
			unhealthyPods++
		}
	}

	if unhealthyPods > 0 {
		return &HealthStatus{
			IsHealthy: false,
			Message:   fmt.Sprintf("%d/%d system pods are not running", unhealthyPods, len(pods.Items)),
		}, nil
	}

	return &HealthStatus{
		IsHealthy: true,
		Message:   fmt.Sprintf("All %d system pods are running", len(pods.Items)),
	}, nil
}