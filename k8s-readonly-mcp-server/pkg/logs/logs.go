package logs

import (
	"context"
	"io"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
)

// LogOptions defines various options for getting logs
type LogOptions struct {
	Container string
	Follow    bool
	Previous  bool
	TailLines *int64
}

// GetLogsStream gets container logs as a stream
// The caller is responsible for closing the returned io.ReadCloser
func GetLogsStream(ctx context.Context, clientset kubernetes.Interface, namespace, podName string, opts LogOptions) (io.ReadCloser, error) {
	logOptions := &corev1.PodLogOptions{
		Container: opts.Container,
		Follow:    opts.Follow,
		Previous:  opts.Previous,
	}

	if opts.TailLines != nil {
		logOptions.TailLines = opts.TailLines
	}

	req := clientset.CoreV1().Pods(namespace).GetLogs(podName, logOptions)
	return req.Stream(ctx)
}