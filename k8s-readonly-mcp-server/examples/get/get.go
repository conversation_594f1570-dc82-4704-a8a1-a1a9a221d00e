package main

import (
	"context"
	"encoding/json"
	"log"
	"os"

	"k8s-readonly-mcp-server/pkg/client"
	"k8s-readonly-mcp-server/pkg/get"
)

func main() {
	cli, err := client.NewK8sClient()
	if err != nil {
		log.Fatalf("❗ 连接集群失败: %v", err)
	}
	ctx := context.Background()

	list, err := get.KubectlGet(ctx, cli.DynamicClient, "pods", "default", "")
	if err != nil {
		log.Fatalf("❗ 拉取资源失败: %v", err)
	}
	out, _ := json.MarshalIndent(list, "", "  ")
	os.Stdout.Write(out)
}
