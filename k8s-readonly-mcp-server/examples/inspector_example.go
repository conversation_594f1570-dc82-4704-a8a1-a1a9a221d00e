package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"k8s-readonly-mcp-server/pkg/client"
	"k8s-readonly-mcp-server/pkg/inspector"
)

func main() {
	// 创建 Kubernetes 客户端
	k8sClient, err := client.NewK8sClient("")
	if err != nil {
		log.Fatalf("Failed to create Kubernetes client: %v", err)
	}

	// 创建 Inspector 实例
	ins := inspector.NewInspector(k8sClient.Clientset, k8sClient.DynamicClient)

	ctx := context.Background()

	fmt.Println("=== Kubernetes Cluster Inspector Demo ===\n")

	// 1. 获取集群快速摘要
	fmt.Println("1. Quick Cluster Summary:")
	summary, err := ins.GetQuickSummary(ctx)
	if err != nil {
		log.Printf("Error getting summary: %v", err)
	} else {
		fmt.Printf("   Nodes: %v\n", summary["total_nodes"])
		fmt.Printf("   Namespaces: %v\n", summary["total_namespaces"])
		fmt.Printf("   Pods: %v\n", summary["total_pods"])
		fmt.Printf("   Pod Phases: %v\n", summary["pod_phases"])
	}

	// 2. 获取完整集群快照
	fmt.Println("\n2. Full Cluster Snapshot:")
	snapshot, err := ins.GetFullClusterSnapshot(ctx)
	if err != nil {
		log.Printf("Error getting full snapshot: %v", err)
	} else {
		fmt.Printf("   Total resources collected: %d\n", snapshot.ResourceCounts["total"])
		fmt.Printf("   Timestamp: %v\n", snapshot.Timestamp.Format("2006-01-02 15:04:05"))
		fmt.Printf("   Errors encountered: %d\n", len(snapshot.ErrorSummary))
		
		// Print resource counts
		fmt.Println("\n   Resource Counts:")
		for resource, count := range snapshot.ResourceCounts {
			if resource != "total" {
				fmt.Printf("     %-20s: %d\n", resource, count)
			}
		}
	}

	// 3. 获取特定命名空间快照
	fmt.Println("\n3. Default Namespace Snapshot:")
	nsSnapshot, err := ins.GetNamespaceSnapshot(ctx, "default")
	if err != nil {
		log.Printf("Error getting namespace snapshot: %v", err)
	} else {
		fmt.Printf("   Resources in default namespace: %d\n", nsSnapshot.ResourceCounts["total"])
	}

	// 4. 搜索资源
	fmt.Println("\n4. Searching for nginx-related resources:")
	searchResults, err := ins.SearchResources(ctx, "nginx")
	if err != nil {
		log.Printf("Error searching resources: %v", err)
	} else {
		for resourceType, items := range searchResults {
			fmt.Printf("   Found %d %s containing 'nginx'\n", len(items), resourceType)
		}
	}

	// 5. 导出快照到文件
	if snapshot != nil {
		jsonOutput, err := ins.ExportToJSON(snapshot)
		if err != nil {
			log.Printf("Error exporting to JSON: %v", err)
		} else {
			fileName := "cluster_snapshot.json"
			err = os.WriteFile(fileName, []byte(jsonOutput), 0644)
			if err != nil {
				log.Printf("Error writing file: %v", err)
			} else {
				fmt.Printf("\n5. Full snapshot exported to: %s\n", fileName)
			}
		}
	}

	fmt.Println("\n=== Inspection Complete ===")
}