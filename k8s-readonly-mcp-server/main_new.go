package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"

	"k8s-readonly-mcp-server/pkg/client"
	"k8s-readonly-mcp-server/pkg/describe"
	"k8s-readonly-mcp-server/pkg/health"
	"k8s-readonly-mcp-server/pkg/inspector"
)

func main() {
	// 创建一个新的 MCP 服务器
	s := server.NewMCPServer(
		"K8S ReadOnly Inspector MCP Server",
		"1.0.0",
		server.WithResourceCapabilities(true, true),
		server.WithToolCapabilities(true),
		server.WithPromptCapabilities(true),
		server.WithLogging(),
	)

	// 创建 Kubernetes 客户端
	k8sClient, err := client.NewK8sClient("")
	if err != nil {
		log.Fatalf("Failed to create Kubernetes client: %v", err)
	}

	// 创建 Inspector 实例
	inspectorInstance := inspector.NewInspector(k8sClient.Clientset, k8sClient.DynamicClient)

	// 定义工具
	tools := []*mcp.Tool{
		// 集群检查工具
		mcp.NewTool(
			"cluster_inspect",
			mcp.WithDescription("Get comprehensive read-only snapshot of the entire Kubernetes cluster including all resources, events, and statistics"),
		),
		// 命名空间检查工具
		mcp.NewTool(
			"namespace_inspect",
			mcp.WithDescription("Get comprehensive read-only snapshot of a specific Kubernetes namespace"),
			mcp.WithString("namespace",
				mcp.Required(),
				mcp.Description("The namespace to inspect"),
			),
		),
		// 集群健康检查
		mcp.NewTool(
			"cluster_health",
			mcp.WithDescription("Check the health of the Kubernetes cluster including nodes and system components"),
		),
		// 资源描述工具
		mcp.NewTool(
			"kubectl_describe",
			mcp.WithDescription("Describe Kubernetes resources with aggregated information including related events"),
			mcp.WithString("resource",
				mcp.Required(),
				mcp.Description("Resource type to describe (e.g., pods, deployments, services)"),
			),
			mcp.WithString("namespace",
				mcp.Required(),
				mcp.Description("Namespace of the resource"),
			),
			mcp.WithString("name",
				mcp.Required(),
				mcp.Description("Name of the resource"),
			),
		),
		// 快速摘要工具
		mcp.NewTool(
			"cluster_summary",
			mcp.WithDescription("Get a quick summary of the cluster including node, namespace, and pod counts"),
		),
		// 资源搜索工具
		mcp.NewTool(
			"search_resources",
			mcp.WithDescription("Search for Kubernetes resources containing a specific term in names, labels, or annotations"),
			mcp.WithString("term",
				mcp.Required(),
				mcp.Description("Search term to look for in resource metadata"),
			),
		),
	}

	// 注册工具处理器
	handlers := []func(context.Context, mcp.CallToolRequest) (*mcp.CallToolResult, error){
		func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			return handleClusterInspect(ctx, req, inspectorInstance)
		},
		func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			return handleNamespaceInspect(ctx, req, inspectorInstance)
		},
		func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			return handleClusterHealth(ctx, req, k8sClient.Clientset)
		},
		func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			return handleKubectlDescribe(ctx, req, k8sClient.Clientset)
		},
		func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			return handleClusterSummary(ctx, req, inspectorInstance)
		},
		func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			return handleSearchResources(ctx, req, inspectorInstance)
		},
	}

	// 注册所有工具
	for i, tool := range tools {
		s.AddTool(tool, handlers[i])
	}

	// 启动 MCP Server 服务
	port := ":8080"
	log.Printf("Starting K8S ReadOnly Inspector MCP Server on %s", port)
	httpServer := server.NewStreamableHTTPServer(s)

	// 启动 HTTP server
	go func() {
		if err := httpServer.Start(port); err != nil && err != http.ErrServerClosed {
			log.Fatal(err)
		}
	}()

	log.Println("Server started successfully!")
	log.Println("Available tools:")
	for _, tool := range tools {
		log.Printf("- %s: %s", tool.Name, tool.Description)
	}

	// 优雅退出
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// 关闭 HTTP server
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := httpServer.Shutdown(shutdownCtx); err != nil {
		log.Printf("HTTP server shutdown error: %v", err)
	} else {
		log.Println("Server gracefully stopped")
	}
}

// 处理集群检查
func handleClusterInspect(ctx context.Context, req mcp.CallToolRequest, ins *inspector.Inspector) (*mcp.CallToolResult, error) {
	snapshot, err := ins.GetFullClusterSnapshot(ctx)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to inspect cluster: %v", err)), nil
	}

	jsonData, err := json.MarshalIndent(snapshot, "", "  ")
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to format output: %v", err)), nil
	}

	return mcp.NewToolResultText(string(jsonData)), nil
}

// 处理命名空间检查
func handleNamespaceInspect(ctx context.Context, req mcp.CallToolRequest, ins *inspector.Inspector) (*mcp.CallToolResult, error) {
	namespace, err := req.RequireString("namespace")
	if err != nil {
		return mcp.NewToolResultError("namespace parameter is required"), nil
	}

	snapshot, err := ins.GetNamespaceSnapshot(ctx, namespace)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to inspect namespace %s: %v", namespace, err)), nil
	}

	jsonData, err := json.MarshalIndent(snapshot, "", "  ")
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to format output: %v", err)), nil
	}

	return mcp.NewToolResultText(string(jsonData)), nil
}

// 处理集群健康检查
func handleClusterHealth(ctx context.Context, req mcp.CallToolRequest, clientset kubernetes.Interface) (*mcp.CallToolResult, error) {
	report, err := health.CheckClusterHealth(ctx, clientset)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to check cluster health: %v", err)), nil
	}

	jsonData, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to format output: %v", err)), nil
	}

	return mcp.NewToolResultText(string(jsonData)), nil
}

// 处理资源描述
func handleKubectlDescribe(ctx context.Context, req mcp.CallToolRequest, clientset kubernetes.Interface) (*mcp.CallToolResult, error) {
	resource, err := req.RequireString("resource")
	if err != nil {
		return mcp.NewToolResultError("resource parameter is required"), nil
	}

	namespace, err := req.RequireString("namespace")
	if err != nil {
		return mcp.NewToolResultError("namespace parameter is required"), nil
	}

	name, err := req.RequireString("name")
	if err != nil {
		return mcp.NewToolResultError("name parameter is required"), nil
	}

	if resource == "pods" || resource == "pod" {
		desc, err := describe.DescribePod(ctx, clientset, namespace, name)
		if err != nil {
			if errors.IsNotFound(err) {
				return mcp.NewToolResultError(fmt.Sprintf("Pod %s not found in namespace %s", name, namespace)), nil
			}
			return mcp.NewToolResultError(fmt.Sprintf("Failed to describe pod: %v", err)), nil
		}

		jsonData, err := json.MarshalIndent(desc, "", "  ")
		if err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("Failed to format output: %v", err)), nil
		}

		return mcp.NewToolResultText(string(jsonData)), nil
	}

	return mcp.NewToolResultError(fmt.Sprintf("Unsupported resource type for describe: %s", resource)), nil
}

// 处理集群摘要
func handleClusterSummary(ctx context.Context, req mcp.CallToolRequest, ins *inspector.Inspector) (*mcp.CallToolResult, error) {
	summary, err := ins.GetQuickSummary(ctx)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to get cluster summary: %v", err)), nil
	}

	jsonData, err := json.MarshalIndent(summary, "", "  ")
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to format output: %v", err)), nil
	}

	return mcp.NewToolResultText(string(jsonData)), nil
}

// 处理资源搜索
func handleSearchResources(ctx context.Context, req mcp.CallToolRequest, ins *inspector.Inspector) (*mcp.CallToolResult, error) {
	term, err := req.RequireString("term")
	if err != nil {
		return mcp.NewToolResultError("term parameter is required"), nil
	}

	results, err := ins.SearchResources(ctx, term)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to search resources: %v", err)), nil
	}

	jsonData, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("Failed to format output: %v", err)), nil
	}

	return mcp.NewToolResultText(string(jsonData)), nil
}